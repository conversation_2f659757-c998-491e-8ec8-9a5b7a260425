/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

/* Layout components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;
}

.col {
  padding: 10px;
}

.col-12 { flex: 0 0 100%; }
.col-6 { flex: 0 0 50%; }
.col-4 { flex: 0 0 33.333%; }
.col-3 { flex: 0 0 25%; }

@media (max-width: 768px) {
  .col-6, .col-4, .col-3 {
    flex: 0 0 100%;
  }
}

/* Header styles */
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.07);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  z-index: 100;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #2c3e50;
}

.logo img {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
}

.nav {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav a {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s;
}

.nav a:hover {
  color: #1890ff;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.btn:hover {
  background: #40a9ff;
}

.btn-large {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 10px;
  margin: 8px;
}

/* Card styles */
.card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.07);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.card-cover img {
  width: 100%;
  height: auto;
  object-fit: contain;
  background: #f5f5f5;
}

.card-body {
  padding: 20px;
  text-align: center;
}

.card-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
}

.card-description {
  color: #666;
  line-height: 1.5;
}

/* Footer styles */
.footer {
  background: #2c3e50;
  color: white;
  padding: 50px 0 20px;
  margin-top: 48px;
}

.footer h3 {
  color: white;
  margin-bottom: 20px;
}

.footer ul {
  list-style: none;
  padding: 0;
}

.footer a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s;
}

.footer a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #444;
  color: #aaa;
  margin-top: 40px;
}

/* Section styles */
.section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 50px;
  color: #2c3e50;
}

.hero {
  background: #f7f9fb;
  min-height: 100vh;
  padding: 80px 0 40px 0;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #2c3e50;
}

.hero p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: #666;
}

/* Utility classes */
.text-center { text-align: center; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mb-50 { margin-bottom: 50px; }
.mt-48 { margin-top: 48px; }

.bg-white { background: white; }
.bg-gray { background: #f7f9fb; }

/* Page content styles */
.page-content {
  padding: 2rem 0;
  max-width: 800px;
  margin: 0 auto;
}

.page-content h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.page-content h2 {
  color: #2c3e50;
  margin: 2rem 0 1rem 0;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.5rem;
}

.page-content h3 {
  color: #2c3e50;
  margin: 1.5rem 0 0.5rem 0;
}

.page-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.page-content ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.page-content li {
  margin-bottom: 0.5rem;
}

.page-content strong {
  color: #2c3e50;
  display: block;
  margin: 1rem 0 0.5rem 0;
}

/* FAQ styles */
.faq-item {
  margin-bottom: 30px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
}

.faq-item h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.faq-item p {
  color: #666;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .nav {
    display: none;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .section {
    padding: 40px 0;
  }

  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 1rem;
  }
}

