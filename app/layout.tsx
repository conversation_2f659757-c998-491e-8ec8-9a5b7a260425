import type { Metadata } from 'next'
import './globals.css'
import CookieConsent from '../src/components/elements/CookieConsent';

export const metadata: Metadata = {
  title: 'Multi Run - Parallel Dual App',
  description: 'Multi Run​​ is a powerful tool that allows you to run multiple accounts or applications simultaneously on a single device.',
  keywords: 'Multi Run,Parallel Space,Dual App,Multiple Accounts,Cloner,Virtual',
  icons: {
    icon: '/logo.webp',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body suppressHydrationWarning={true}>
        {children}
        <CookieConsent />
      </body>
    </html>
  )
}