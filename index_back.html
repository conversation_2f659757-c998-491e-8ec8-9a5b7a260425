<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi Run - Parallel Dual App</title>
    <meta name="description"
        content="Multi Run​​ is a powerful tool that allows you to run multiple accounts or applications simultaneously on a single device.">
    <meta name="keywords" content="Multi Run,Parallel Space,Dual App,Multiple Accounts">
    <link rel="icon" href="logo.webp" type="image/webp">
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-74Q8HLBVL9"></script>
    <script>
        // Google Consent Mode v2 初始化
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        // 默认全部拒绝
        gtag('consent', 'default', {
            'ad_storage': 'denied',
            'analytics_storage': 'denied',
            'ad_user_data': 'denied',
            'ad_personalization': 'denied'
        });
    </script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(207deg, rgba(255, 231, 218, 0.34) 0%, rgba(237, 234, 254, 0.4) 27%, rgba(213, 230, 255, 0.304) 40%, rgba(255, 255, 255, 0) 100%), #F5F7FC;

        }

        /* 导航栏样式 */
        .navbar {
            background: #fff;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 90%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .logo-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-grow: 1;
        }

        .logo {
            color: black;
            text-decoration: none;
            font-size: 18px;
            font-weight: 600;

        }

        .nav-links {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .nav-links a {
            color: black;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: #3498db;
        }

        /* 主要内容区域 */
        .main-content {
            margin-top: 80px;
            padding: 20px;
        }

        /* 左侧功能模块 */
        .app-showcase {
            display: flex;
            max-width: 1400px;
            /* 从1200px扩大 */
            margin: 0 auto;
            gap: 40px;
            padding: 20px;
        }

        .main-content {
            max-width: 1400px;
            /* 保持整体布局协调 */
            margin: 80px auto 0;
            padding: 20px;
        }

        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            /* 让子元素靠左对齐 */
            text-align: left;
            /* 文本内容靠左 */
            justify-content: center;
        }

        .app-title {
            width: 100%;
            text-align: left;
            /* 标题也靠左 */
        }

        .app-title,
        .description-text {
            width: 100%;
            /* 确保标题和描述文本能正确居中 */
        }

        .app-title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .action-button {
            display: inline-block;
            text-decoration: none;
            /* 保留原有按钮样式 */
            padding: 12px 48px;
            border-radius: 25px;
            background: #3498db;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            background: linear-gradient(274deg, #A67AFF 3%, #43A8F0 96%);

            box-shadow: 0px 4px 10px 0px rgba(45, 94, 155, 0.3);
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* 右侧图文区 */
        .right-section {
            width: 600px;
        }

        .carousel {
            width: 100%;
            height: 500px;
            /* 匹配图片高度 */
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
        }

        .carousel img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            /* 将cover改为contain */
            object-position: center;
        }

        .description {
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .description p {
            font-size: 15px;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        /* 详情图陈列区 */
        .feature-grid {
            max-width: 1200px;
            margin: 40px auto;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            padding: 20px;
        }

        .feature-item {
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .feature-item:hover {
            transform: scale(1.05);
        }

        .feature-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .feature-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            color: white;
        }

        .feature-overlay p {
            font-size: 14px;
            margin: 0;
        }

        .description-text {
            font-size: 18px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.8;
        }

        .feature-list {
            list-style-type: none;
            padding-left: 0;
            font-size: 18px;
            color: #666;
            line-height: 1.8;
        }

        .feature-list li {
            margin-bottom: 10px;
        }

        .feature-list strong {
            color: #2c3e50;
        }


        .action-buttons {
            margin-top: 20px;

        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .feature-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .app-showcase {
                flex-direction: column;
            }

            .right-section {
                width: 100%;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .nav-container {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (min-width: 1600px) {
            .app-showcase {
                max-width: 90%;
            }

            .right-section {
                width: 700px;
            }
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-group">
                <img src="logo.webp" alt="Logo" style="width: 40px; height: 40px;">
                <div class="logo">Multi Run - Parallel Dual App</div>
            </div>
            <div class="nav-links">
                <a href="https://play.google.com/store/apps/details?id=com.dong.multirun">Home</a>
                <a href="https://x.com/JP_DONG">Contact Us</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- APP展示区 -->
        <div class="app-showcase">
            <div class="left-section">
                <h1 class="app-title">Multi Run</h1>
                <p class="description-text">
                    Tired of switching between apps?
                    <br>
                    <strong>Multi Run</strong> lets you run multiple accounts or applications
                    <strong>simultaneously</strong> on a single device—seamlessly!
                </p>
                <ul class="feature-list">
                    <li>✨ <strong>Key Features:</strong></li>
                    <li>✔ <strong>Clone & Run</strong></li>
                    <li>✔ <strong>Isolated Sessions</strong> </li>
                    <li>✔ <strong>One-Click Switching</strong></li>

                </ul>
                <div class="action-buttons">
                    <a class="action-button" href="https://play.google.com/store/apps/details?id=com.dong.multirun"
                        target="_blank">Try Now</a>
                </div>
            </div>
            <div class="right-section">
                <div class="carousel">
                    <!-- 这里放置轮播图 -->
                    <img src="res/multi_title_image.webp" alt="产品演示">
                </div>

            </div>
        </div>

        <!-- 详情图陈列区 -->
        <div class="feature-grid">
            <div class="feature-item">
                <img src="res/multi_feature_1.webp" alt="Multi Instance" class="feature-image">
                <div class="feature-overlay">
                    <p>Multi Instance</p>
                </div>
            </div>
            <div class="feature-item">
                <img src="res/multi_feature_2.webp" alt="Free to Use" class="feature-image">
                <div class="feature-overlay">
                    <p>Free to Use</p>
                </div>
            </div>
            <div class="feature-item">
                <img src="res/multi_feature_3.webp" alt="Fast and Convenient" class="feature-image">
                <div class="feature-overlay">
                    <p>Fast and Convenient</p>
                </div>
            </div>
            <div class="feature-item">
                <img src="res/multi_feature_4.webp" alt="Instant Boot" class="feature-image">
                <div class="feature-overlay">
                    <p>Instant Boot</p>
                </div>
            </div>
        </div>
    </div>

    <div id="cookie-consent-banner" style="position:fixed;bottom:0;left:0;right:0;background:#fff;padding:20px;box-shadow:0 -2px 8px rgba(0,0,0,0.1);z-index:9999;display:flex;justify-content:space-between;align-items:center;">
        <span>This website uses cookies for analytics and ad personalization. Please agree to our collection of your data to improve your experience。</span>
        <div>
            <button onclick="acceptAllCookies()" style="margin-right:10px; background-color:#165DFF; color:white; border:none; padding:10px 20px; border-radius:8px; cursor:pointer; box-shadow:0 2px 4px rgba(0,0,0,0.1); transition:all 0.3s ease;">Agree All</button>
            <button onclick="rejectAllCookies()" style="background-color:#f5f5f5; color:#333; border:none; padding:10px 20px; border-radius:8px; cursor:pointer; box-shadow:0 2px 4px rgba(0,0,0,0.1); transition:all 0.3s ease;">Only allow necessary</button>
        </div>
    </div>

    <script>
    // 页面加载时检查Cookie同意状态
    document.addEventListener('DOMContentLoaded', function() {
      const consent = localStorage.getItem('cookieConsent');
      const banner = document.getElementById('cookie-consent-banner');
      if (consent) {
        banner.style.display = 'none';
        // 用户同意时初始化Google Analytics
        if (consent === 'accepted') {
          // 请将此处替换为您的GA测量ID
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-74Q8HLBVL9');
          gtag('consent', 'update', {
          'ad_storage': 'granted',
          'analytics_storage': 'granted',
          'ad_user_data': 'granted',
          'ad_personalization': 'granted'
      });
        }
      }
    });

    function acceptAllCookies() {
      localStorage.setItem('cookieConsent', 'accepted');
      document.getElementById('cookie-consent-banner').style.display = 'none';
      // 更新Google Analytics consent状态
      gtag('consent', 'update', {
          'ad_storage': 'granted',
          'analytics_storage': 'granted',
          'ad_user_data': 'granted',
          'ad_personalization': 'granted'
      });
      // 初始化Google Analytics
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-74Q8HLBVL9'); // 替换为您的GA测量ID
    }

    function rejectAllCookies() {
      localStorage.setItem('cookieConsent', 'rejected');
      document.getElementById('cookie-consent-banner').style.display = 'none';
      // 更新Google Analytics consent状态
      gtag('consent', 'update', {
          'ad_storage': 'denied',
          'analytics_storage': 'denied',
          'ad_user_data': 'denied',
          'ad_personalization': 'denied'
      });
    }
    </script>
</body>

</html>