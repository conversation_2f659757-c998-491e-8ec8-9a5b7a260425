/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var antd_dist_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! antd/dist/reset.css */ \"(rsc)/./node_modules/antd/dist/reset.css\");\n/* harmony import */ var _src_components_elements_CookieConsent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/elements/CookieConsent */ \"(rsc)/./src/components/elements/CookieConsent.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'MultiRun - Multiple Accounts Manager',\n    description: 'Clone and run multiple instances of the same app with separate data storage.'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_elements_CookieConsent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFIc0I7QUFDd0M7QUFJN0QsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsMkpBQWU7OzhCQUM5Qiw4REFBQ0MsOEVBQWFBOzs7OztnQkFDYks7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnYW50ZC9kaXN0L3Jlc2V0LmNzcydcbmltcG9ydCBDb29raWVDb25zZW50IGZyb20gJy4uL3NyYy9jb21wb25lbnRzL2VsZW1lbnRzL0Nvb2tpZUNvbnNlbnQnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdNdWx0aVJ1biAtIE11bHRpcGxlIEFjY291bnRzIE1hbmFnZXInLFxuICBkZXNjcmlwdGlvbjogJ0Nsb25lIGFuZCBydW4gbXVsdGlwbGUgaW5zdGFuY2VzIG9mIHRoZSBzYW1lIGFwcCB3aXRoIHNlcGFyYXRlIGRhdGEgc3RvcmFnZS4nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPENvb2tpZUNvbnNlbnQgLz5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiQ29va2llQ29uc2VudCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_pages_MultipleAccountsPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/pages/MultipleAccountsPage */ \"(rsc)/./src/pages/MultipleAccountsPage.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_pages_MultipleAccountsPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0U7QUFFckQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHVFQUFvQkE7Ozs7O0FBQzlCIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE11bHRpcGxlQWNjb3VudHNQYWdlIGZyb20gJy4uL3NyYy9wYWdlcy9NdWx0aXBsZUFjY291bnRzUGFnZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIDxNdWx0aXBsZUFjY291bnRzUGFnZSAvPlxufVxuIl0sIm5hbWVzIjpbIk11bHRpcGxlQWNjb3VudHNQYWdlIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/elements/CookieConsent.tsx */ \"(rsc)/./src/components/elements/CookieConsent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmRpc3QlMkZyZXNldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkb25namlhbmdwZW5nJTJGRG9jdW1lbnRzJTJGV29ya1NwYWNlJTJGV2ViJTJGTXVsdGlSdW4lMkZzcmMlMkZjb21wb25lbnRzJTJGZWxlbWVudHMlMkZDb29raWVDb25zZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2VsZW1lbnRzL0Nvb2tpZUNvbnNlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js */ \"(rsc)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/icons/es/components/AntdIcon.js */ \"(rsc)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/CookieConsent.tsx":
/*!***************************************************!*\
  !*** ./src/components/elements/CookieConsent.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/elements/DownloadButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/elements/DownloadButton.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!antd */ \"(rsc)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js\");\n\n\n\nconst DownloadButton = ({ icon, text, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_antd__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        type: \"primary\",\n        icon: icon,\n        size: \"large\",\n        href: href,\n        style: {\n            borderRadius: 10,\n            margin: 8\n        },\n        children: text\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/DownloadButton.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9Eb3dubG9hZEJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNJO0FBUTlCLE1BQU1FLGlCQUFnRCxDQUFDLEVBQUVDLElBQUksRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQUUsaUJBQ3pFLDhEQUFDSixzRUFBTUE7UUFBQ0ssTUFBSztRQUFVSCxNQUFNQTtRQUFNSSxNQUFLO1FBQVFGLE1BQU1BO1FBQU1HLE9BQU87WUFBRUMsY0FBYztZQUFJQyxRQUFRO1FBQUU7a0JBQzlGTjs7Ozs7O0FBSUwsaUVBQWVGLGNBQWNBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2VsZW1lbnRzL0Rvd25sb2FkQnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnYW50ZCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgRG93bmxvYWRCdXR0b25Qcm9wcyB7XG4gIGljb24/OiBSZWFjdC5SZWFjdE5vZGU7XG4gIHRleHQ6IHN0cmluZztcbiAgaHJlZjogc3RyaW5nO1xufVxuXG5jb25zdCBEb3dubG9hZEJ1dHRvbjogUmVhY3QuRkM8RG93bmxvYWRCdXR0b25Qcm9wcz4gPSAoeyBpY29uLCB0ZXh0LCBocmVmIH0pID0+IChcbiAgPEJ1dHRvbiB0eXBlPVwicHJpbWFyeVwiIGljb249e2ljb259IHNpemU9XCJsYXJnZVwiIGhyZWY9e2hyZWZ9IHN0eWxlPXt7IGJvcmRlclJhZGl1czogMTAsIG1hcmdpbjogOCB9fT5cbiAgICB7dGV4dH1cbiAgPC9CdXR0b24+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBEb3dubG9hZEJ1dHRvbjsgIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiRG93bmxvYWRCdXR0b24iLCJpY29uIiwidGV4dCIsImhyZWYiLCJ0eXBlIiwic2l6ZSIsInN0eWxlIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/DownloadButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/FAQItem.tsx":
/*!*********************************************!*\
  !*** ./src/components/elements/FAQItem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Collapse_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Collapse!=!antd */ \"(rsc)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js\");\n\n\n\nconst FAQItem = ({ question, answer })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Collapse_antd__WEBPACK_IMPORTED_MODULE_2__.Collapse, {\n        bordered: false,\n        style: {\n            marginBottom: 16\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Collapse_antd__WEBPACK_IMPORTED_MODULE_2__.Collapse.Panel, {\n            header: question,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: '#666'\n                },\n                children: answer\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FAQItem.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined)\n        }, \"1\", false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FAQItem.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FAQItem.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FAQItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9GQVFJdGVtLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ007QUFPaEMsTUFBTUUsVUFBa0MsQ0FBQyxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRSxpQkFDM0QsOERBQUNILDBFQUFRQTtRQUFDSSxVQUFVO1FBQU9DLE9BQU87WUFBRUMsY0FBYztRQUFHO2tCQUNuRCw0RUFBQ04sMEVBQVFBLENBQUNPLEtBQUs7WUFBQ0MsUUFBUU47c0JBQ3RCLDRFQUFDTztnQkFBSUosT0FBTztvQkFBRUssT0FBTztnQkFBTzswQkFBSVA7Ozs7OztXQURJOzs7Ozs7Ozs7O0FBTTFDLGlFQUFlRixPQUFPQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9GQVFJdGVtLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ29sbGFwc2UgfSBmcm9tICdhbnRkJztcblxuZXhwb3J0IGludGVyZmFjZSBGQVFJdGVtUHJvcHMge1xuICBxdWVzdGlvbjogc3RyaW5nO1xuICBhbnN3ZXI6IHN0cmluZztcbn1cblxuY29uc3QgRkFRSXRlbTogUmVhY3QuRkM8RkFRSXRlbVByb3BzPiA9ICh7IHF1ZXN0aW9uLCBhbnN3ZXIgfSkgPT4gKFxuICA8Q29sbGFwc2UgYm9yZGVyZWQ9e2ZhbHNlfSBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDE2IH19PlxuICAgIDxDb2xsYXBzZS5QYW5lbCBoZWFkZXI9e3F1ZXN0aW9ufSBrZXk9XCIxXCI+XG4gICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnIzY2NicgfX0+e2Fuc3dlcn08L2Rpdj5cbiAgICA8L0NvbGxhcHNlLlBhbmVsPlxuICA8L0NvbGxhcHNlPlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgRkFRSXRlbTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbGxhcHNlIiwiRkFRSXRlbSIsInF1ZXN0aW9uIiwiYW5zd2VyIiwiYm9yZGVyZWQiLCJzdHlsZSIsIm1hcmdpbkJvdHRvbSIsIlBhbmVsIiwiaGVhZGVyIiwiZGl2IiwiY29sb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/FAQItem.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/FeatureCard.tsx":
/*!*************************************************!*\
  !*** ./src/components/elements/FeatureCard.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Card_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Card!=!antd */ \"(rsc)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js\");\n\n\n\nconst FeatureCard = ({ img, title, desc })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_antd__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        hoverable: true,\n        cover: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            alt: title,\n            src: img,\n            style: {\n                width: '100%',\n                height: 'auto',\n                objectFit: 'contain',\n                margin: '0 auto',\n                background: '#f5f5f5',\n                borderRadius: 10,\n                marginTop: 0\n            }\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n            lineNumber: 13,\n            columnNumber: 12\n        }, void 0),\n        style: {\n            borderRadius: 10,\n            textAlign: 'center',\n            boxShadow: '0 3px 10px rgba(0,0,0,0.07)',\n            marginBottom: 24\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_antd__WEBPACK_IMPORTED_MODULE_2__.Card.Meta, {\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#2c3e50',\n                    fontWeight: 600\n                },\n                children: title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 14\n            }, void 0),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#666'\n                },\n                children: desc\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                lineNumber: 18,\n                columnNumber: 20\n            }, void 0),\n            style: {\n                minHeight: 100,\n                overflow: 'hidden' // 如果内容可能溢出，可以隐藏\n            }\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n            lineNumber: 16,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/FeatureCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/Footer.tsx":
/*!********************************************!*\
  !*** ./src/components/elements/Footer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Col,Layout,Row!=!antd */ \"(rsc)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nconst { Footer: AntFooter } = _barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__.Layout;\nconst Footer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AntFooter, {\n        style: {\n            background: '#2c3e50',\n            color: 'white',\n            padding: '50px 0 20px',\n            marginTop: 48\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: 1200,\n                margin: '0 auto',\n                padding: '0 20px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__.Row, {\n                    gutter: [\n                        32,\n                        32\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__.Col, {\n                            xs: 24,\n                            md: 8,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: 'white',\n                                        marginBottom: 20\n                                    },\n                                    children: \"Multi Run\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 12,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Run multiple accounts and apps simultaneously on one device with complete data isolation.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__.Col, {\n                            xs: 24,\n                            md: 8,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: 'white',\n                                        marginBottom: 20\n                                    },\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    style: {\n                                        listStyle: 'none',\n                                        padding: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#features\",\n                                                style: footerLinkStyle,\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#download\",\n                                                style: footerLinkStyle,\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_Layout_Row_antd__WEBPACK_IMPORTED_MODULE_3__.Col, {\n                            xs: 24,\n                            md: 8,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: 'white',\n                                        marginBottom: 20\n                                    },\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    style: {\n                                        listStyle: 'none',\n                                        padding: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/privacy-policy\",\n                                                style: footerLinkStyle,\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/terms-of-use\",\n                                                style: footerLinkStyle,\n                                                children: \"Terms of Use\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        paddingTop: 20,\n                        borderTop: '1px solid #444',\n                        color: '#aaa',\n                        marginTop: 40\n                    },\n                    children: \"\\xa9 2024 Multi Run. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\nconst footerLinkStyle = {\n    color: '#ddd',\n    textDecoration: 'none',\n    transition: 'color 0.3s'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/NavBar.tsx":
/*!********************************************!*\
  !*** ./src/components/elements/NavBar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Button_Layout_Menu_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Layout,Menu!=!antd */ \"(rsc)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst { Header } = _barrel_optimize_names_Button_Layout_Menu_antd__WEBPACK_IMPORTED_MODULE_2__.Layout;\nconst menuItems = [\n    {\n        key: 'features',\n        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: \"/#features\",\n            children: \"Features\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 7,\n            columnNumber: 29\n        }, undefined)\n    },\n    {\n        key: 'download',\n        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: \"/#download\",\n            children: \"Download\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 8,\n            columnNumber: 29\n        }, undefined)\n    },\n    {\n        key: 'faq',\n        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: \"/#faq\",\n            children: \"FAQ\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 9,\n            columnNumber: 24\n        }, undefined)\n    },\n    {\n        key: 'testimonials',\n        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: \"/#testimonials\",\n            children: \"Testimonials\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 10,\n            columnNumber: 33\n        }, undefined)\n    },\n    //{ key: 'rank', label: <Link to=\"/rank\">Rank</Link> },\n    {\n        key: 'contact',\n        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: \"/contact\",\n            children: \"Contact\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 28\n        }, undefined)\n    }\n];\nconst NavBar = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n        style: {\n            background: '#fff',\n            boxShadow: '0 2px 10px rgba(0,0,0,0.07)',\n            padding: '0 24px',\n            display: 'flex',\n            alignItems: 'center',\n            zIndex: 100\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        textDecoration: 'none'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/logo.webp\",\n                            alt: \"Multiple Accounts Logo\",\n                            style: {\n                                width: 40,\n                                height: 40,\n                                marginRight: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                fontSize: 24,\n                                fontWeight: 'bold',\n                                color: '#2c3e50'\n                            },\n                            children: \"Multi Run\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                lineNumber: 17,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Layout_Menu_antd__WEBPACK_IMPORTED_MODULE_2__.Menu, {\n                mode: \"horizontal\",\n                items: menuItems,\n                style: {\n                    flex: 2,\n                    minWidth: 400,\n                    borderBottom: 'none',\n                    justifyContent: 'center'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Layout_Menu_antd__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                type: \"primary\",\n                href: \"/#download\",\n                style: {\n                    marginLeft: 24,\n                    borderRadius: 5\n                },\n                children: \"Download Now\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                lineNumber: 24,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/NavBar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/SectionTitle.tsx":
/*!**************************************************!*\
  !*** ./src/components/elements/SectionTitle.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SectionTitle = ({ children, style })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n        style: {\n            textAlign: 'center',\n            fontSize: '2rem',\n            marginBottom: 50,\n            color: '#2c3e50',\n            ...style\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/SectionTitle.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SectionTitle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9TZWN0aW9uVGl0bGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQUUxQixNQUFNQyxlQUFtRixDQUFDLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFLGlCQUMzRyw4REFBQ0M7UUFBR0QsT0FBTztZQUFFRSxXQUFXO1lBQVVDLFVBQVU7WUFBUUMsY0FBYztZQUFJQyxPQUFPO1lBQVcsR0FBR0wsS0FBSztRQUFDO2tCQUFJRDs7Ozs7O0FBR3ZHLGlFQUFlRCxZQUFZQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9TZWN0aW9uVGl0bGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IFNlY3Rpb25UaXRsZTogUmVhY3QuRkM8UmVhY3QuUHJvcHNXaXRoQ2hpbGRyZW48eyBzdHlsZT86IFJlYWN0LkNTU1Byb3BlcnRpZXMgfT4+ID0gKHsgY2hpbGRyZW4sIHN0eWxlIH0pID0+IChcbiAgPGgyIHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIGZvbnRTaXplOiAnMnJlbScsIG1hcmdpbkJvdHRvbTogNTAsIGNvbG9yOiAnIzJjM2U1MCcsIC4uLnN0eWxlIH19PntjaGlsZHJlbn08L2gyPlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgU2VjdGlvblRpdGxlOyAiXSwibmFtZXMiOlsiUmVhY3QiLCJTZWN0aW9uVGl0bGUiLCJjaGlsZHJlbiIsInN0eWxlIiwiaDIiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsImNvbG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/SectionTitle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/TestimonialCard.tsx":
/*!*****************************************************!*\
  !*** ./src/components/elements/TestimonialCard.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Card_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Card!=!antd */ \"(rsc)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js\");\n\n\n\nconst TestimonialCard = ({ avatar, name, text })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Card_antd__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        style: {\n            borderRadius: 10,\n            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',\n            marginBottom: 24\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Card_antd__WEBPACK_IMPORTED_MODULE_2__.Card.Meta, {\n            avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Card_antd__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                src: avatar,\n                size: 48\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                lineNumber: 13,\n                columnNumber: 15\n            }, void 0),\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    fontWeight: 'bold',\n                    color: '#2c3e50'\n                },\n                children: name\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 14\n            }, void 0),\n            description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#666',\n                    fontSize: '1rem'\n                },\n                children: text\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                lineNumber: 15,\n                columnNumber: 20\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n            lineNumber: 12,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestimonialCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/TestimonialCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Column.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Column.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Col_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Col!=!antd */ \"(rsc)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js\");\n\n\n\nconst Column = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Col_antd__WEBPACK_IMPORTED_MODULE_2__.Col, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Column.tsx\",\n        lineNumber: 4,\n        columnNumber: 47\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Column);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvQ29sdW1uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ1c7QUFFckMsTUFBTUUsU0FBNkIsQ0FBQ0Msc0JBQVUsOERBQUNGLGdFQUFHQTtRQUFFLEdBQUdFLEtBQUs7Ozs7OztBQUU1RCxpRUFBZUQsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L0NvbHVtbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENvbCwgQ29sUHJvcHMgfSBmcm9tICdhbnRkJztcblxuY29uc3QgQ29sdW1uOiBSZWFjdC5GQzxDb2xQcm9wcz4gPSAocHJvcHMpID0+IDxDb2wgey4uLnByb3BzfSAvPjtcblxuZXhwb3J0IGRlZmF1bHQgQ29sdW1uOyAiXSwibmFtZXMiOlsiUmVhY3QiLCJDb2wiLCJDb2x1bW4iLCJwcm9wcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Column.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Container.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/Container.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Container = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: 1200,\n            margin: '0 auto',\n            padding: '0 20px'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Container.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Container);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvQ29udGFpbmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFMUIsTUFBTUMsWUFBK0MsQ0FBQyxFQUFFQyxRQUFRLEVBQUUsaUJBQ2hFLDhEQUFDQztRQUFJQyxPQUFPO1lBQUVDLFVBQVU7WUFBTUMsUUFBUTtZQUFVQyxTQUFTO1FBQVM7a0JBQUlMOzs7Ozs7QUFHeEUsaUVBQWVELFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2xheW91dC9Db250YWluZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IENvbnRhaW5lcjogUmVhY3QuRkM8UmVhY3QuUHJvcHNXaXRoQ2hpbGRyZW4+ID0gKHsgY2hpbGRyZW4gfSkgPT4gKFxuICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAxMjAwLCBtYXJnaW46ICcwIGF1dG8nLCBwYWRkaW5nOiAnMCAyMHB4JyB9fT57Y2hpbGRyZW59PC9kaXY+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBDb250YWluZXI7ICJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbnRhaW5lciIsImNoaWxkcmVuIiwiZGl2Iiwic3R5bGUiLCJtYXhXaWR0aCIsIm1hcmdpbiIsInBhZGRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Container.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Row.tsx":
/*!***************************************!*\
  !*** ./src/components/layout/Row.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Row_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Row!=!antd */ \"(rsc)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js\");\n\n\n\nconst Row = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Row_antd__WEBPACK_IMPORTED_MODULE_2__.Row, {\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Row.tsx\",\n        lineNumber: 4,\n        columnNumber: 47\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Row);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUm93LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ29DO0FBRTlELE1BQU1DLE1BQTZCLENBQUNFLHNCQUFVLDhEQUFDRCxnRUFBTUE7UUFBRSxHQUFHQyxLQUFLOzs7Ozs7QUFFL0QsaUVBQWVGLEdBQUdBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2xheW91dC9Sb3cudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBSb3cgYXMgQW50Um93LCBSb3dQcm9wcyBhcyBBbnRSb3dQcm9wcyB9IGZyb20gJ2FudGQnO1xuXG5jb25zdCBSb3c6IFJlYWN0LkZDPEFudFJvd1Byb3BzPiA9IChwcm9wcykgPT4gPEFudFJvdyB7Li4ucHJvcHN9IC8+O1xuXG5leHBvcnQgZGVmYXVsdCBSb3c7ICJdLCJuYW1lcyI6WyJSZWFjdCIsIlJvdyIsIkFudFJvdyIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Row.tsx\n");

/***/ }),

/***/ "(rsc)/./src/pages/MultipleAccountsPage.tsx":
/*!********************************************!*\
  !*** ./src/pages/MultipleAccountsPage.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_elements_NavBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/elements/NavBar */ \"(rsc)/./src/components/elements/NavBar.tsx\");\n/* harmony import */ var _components_elements_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/elements/Footer */ \"(rsc)/./src/components/elements/Footer.tsx\");\n/* harmony import */ var _components_elements_FeatureCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/elements/FeatureCard */ \"(rsc)/./src/components/elements/FeatureCard.tsx\");\n/* harmony import */ var _components_elements_TestimonialCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/elements/TestimonialCard */ \"(rsc)/./src/components/elements/TestimonialCard.tsx\");\n/* harmony import */ var _components_elements_FAQItem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/elements/FAQItem */ \"(rsc)/./src/components/elements/FAQItem.tsx\");\n/* harmony import */ var _components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/elements/SectionTitle */ \"(rsc)/./src/components/elements/SectionTitle.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/layout/Container */ \"(rsc)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_layout_Row__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/layout/Row */ \"(rsc)/./src/components/layout/Row.tsx\");\n/* harmony import */ var _components_layout_Column__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/layout/Column */ \"(rsc)/./src/components/layout/Column.tsx\");\n/* harmony import */ var _barrel_optimize_names_DownloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DownloadOutlined!=!@ant-design/icons */ \"(rsc)/./node_modules/@ant-design/icons/es/icons/DownloadOutlined.js\");\n/* harmony import */ var _components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/elements/DownloadButton */ \"(rsc)/./src/components/elements/DownloadButton.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst features = [\n    {\n        img: '/multi_feature_1.webp',\n        title: 'App Cloning',\n        desc: 'Clone and run multiple instances of the same app with separate data storage.'\n    },\n    {\n        img: '/multi_feature_2.webp',\n        title: 'Data Isolation',\n        desc: 'Keep your accounts secure with isolated data spaces and no cross-contamination.'\n    },\n    {\n        img: '/multi_feature_3.webp',\n        title: 'Parallel Running',\n        desc: 'Run multiple accounts simultaneously without performance issues.'\n    },\n    {\n        img: '/multi_feature_4.webp',\n        title: 'Easy Switching',\n        desc: 'Quickly switch between accounts with our intuitive interface.'\n    }\n];\nconst faqs = [\n    {\n        question: 'How do I clone an app?',\n        answer: 'Simply open Multi Run, select the app you want to clone, and follow the on-screen instructions.'\n    },\n    {\n        question: 'Is Multi Run free?',\n        answer: 'Yes, Multi Run is free to use for all users.'\n    },\n    {\n        question: 'Will my data be safe and isolated?',\n        answer: 'Yes, each cloned app runs in a separate, isolated environment to keep your data secure.'\n    },\n    {\n        question: 'Which platforms are supported?',\n        answer: 'Multi Run is available for Android.'\n    }\n];\nconst testimonials = [\n    {\n        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',\n        name: 'Alex',\n        text: 'Ever since I started using it, I enjoyed every bit of it. It\\'s very simple to use and has a great interface. The experience so far is great. Worth 5 stars!'\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',\n        name: 'Linda',\n        text: \"i can't really believe my eyes it's really totaly free and there is no ads u can add multiple apps with free and quickly thank you so much for the creator 😙😙😍really advice everyone to try it it's better than others apps\"\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/men/65.jpg',\n        name: 'Sam',\n        text: 'Finally an app which works with WhatsApp and with less ads. As an Android developer, I understand the need of a foreground notification. But the app shows two foreground notifications.'\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/women/68.jpg',\n        name: 'Emily',\n        text: 'I tried so many clone apps for this particular app and all didn\\'t work except this one! Simple and plays its role very nicely.'\n    }\n];\nconst MultipleAccountsPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_NavBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 82,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: '#f7f9fb',\n                    minHeight: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            gutter: [\n                                40,\n                                40\n                            ],\n                            align: \"middle\",\n                            style: {\n                                padding: '80px 0 40px 0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    xs: 24,\n                                    md: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: 20,\n                                                color: '#2c3e50'\n                                            },\n                                            children: \"Run Multi Accounts on One Device\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: '1.1rem',\n                                                marginBottom: 30,\n                                                color: '#666'\n                                            },\n                                            children: \"Keep your personal and professional lives separate with our secure app cloning solution. No more switching between accounts - run them all simultaneously!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 35\n                                            }, void 0),\n                                            text: \"Get Started\",\n                                            href: \"https://play.google.com/store/apps/details?id=com.dong.multirun\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    xs: 24,\n                                    md: 12,\n                                    style: {\n                                        textAlign: 'center'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/multi_title_image.webp\",\n                                        alt: \"Multi Run\",\n                                        style: {\n                                            maxWidth: '100%',\n                                            height: 'auto',\n                                            borderRadius: 10,\n                                            boxShadow: '0 4px 16px rgba(52,152,219,0.08)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f9f9f9',\n                            padding: '80px 0'\n                        },\n                        id: \"features\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    gutter: [\n                                        30,\n                                        30\n                                    ],\n                                    children: features.map((f)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                ...f\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, f.title, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 0',\n                            textAlign: 'center'\n                        },\n                        id: \"download\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: \"Download Multi Run\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Available on all major platforms. Get started today!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    text: \"Google Play\",\n                                    href: \"https://play.google.com/store/apps/details?id=com.dong.multirun\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#fff',\n                            padding: '80px 0'\n                        },\n                        id: \"faq\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, undefined),\n                                faqs.map((f)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_FAQItem__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        question: f.question,\n                                        answer: f.answer\n                                    }, f.question, false, {\n                                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f9f9f9',\n                            padding: '80px 0'\n                        },\n                        id: \"testimonials\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: \"What Users Say\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    gutter: [\n                                        30,\n                                        30\n                                    ],\n                                    children: testimonials.map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_TestimonialCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                ...t\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, t.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 83,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 143,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultipleAccountsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/pages/MultipleAccountsPage.tsx\n");

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js":
/*!*******************************************************************************!*\
  !*** __barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Avatar: () => (/* binding */ Avatar),
/* harmony export */   Card: () => (/* binding */ Card)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Avatar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Avatar() from the server but Avatar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Avatar,Card",
"Avatar",
);const Card = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Avatar,Card",
"Card",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js":
/*!**************************************************************************!*\
  !*** __barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Button = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Button",
"Button",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js":
/*!**************************************************************************************!*\
  !*** __barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Button: () => (/* binding */ Button),
/* harmony export */   Layout: () => (/* binding */ Layout),
/* harmony export */   Menu: () => (/* binding */ Menu)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Button = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Button,Layout,Menu",
"Button",
);const Layout = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Button,Layout,Menu",
"Layout",
);const Menu = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Menu() from the server but Menu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Button,Layout,Menu",
"Menu",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js":
/*!************************************************************************!*\
  !*** __barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Card: () => (/* binding */ Card)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Card = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Card",
"Card",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js":
/*!***********************************************************************!*\
  !*** __barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Col: () => (/* binding */ Col)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Col = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Col",
"Col",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Col: () => (/* binding */ Col),
/* harmony export */   Layout: () => (/* binding */ Layout),
/* harmony export */   Row: () => (/* binding */ Row)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Col = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Col() from the server but Col is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Col,Layout,Row",
"Col",
);const Layout = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Col,Layout,Row",
"Layout",
);const Row = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Col,Layout,Row",
"Row",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js":
/*!****************************************************************************!*\
  !*** __barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Collapse: () => (/* binding */ Collapse)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Collapse = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Collapse() from the server but Collapse is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Collapse",
"Collapse",
);

/***/ }),

/***/ "(rsc)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js":
/*!***********************************************************************!*\
  !*** __barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Row: () => (/* binding */ Row)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Row = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Row() from the server but Row is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/node_modules/antd/es/index.js@__barrel_optimize__?names=Row",
"Row",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkb25namlhbmdwZW5nJTJGRG9jdW1lbnRzJTJGV29ya1NwYWNlJTJGV2ViJTJGTXVsdGlSdW4lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkb25namlhbmdwZW5nJTJGRG9jdW1lbnRzJTJGV29ya1NwYWNlJTJGV2ViJTJGTXVsdGlSdW4lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUFrSjtBQUNsSjtBQUNBLDBPQUFxSjtBQUNySjtBQUNBLDBPQUFxSjtBQUNySjtBQUNBLG9SQUEwSztBQUMxSztBQUNBLHdPQUFvSjtBQUNwSjtBQUNBLDRQQUE4SjtBQUM5SjtBQUNBLGtRQUFpSztBQUNqSztBQUNBLHNRQUFtSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/elements/CookieConsent.tsx */ \"(ssr)/./src/components/elements/CookieConsent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmRpc3QlMkZyZXNldC5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkb25namlhbmdwZW5nJTJGRG9jdW1lbnRzJTJGV29ya1NwYWNlJTJGV2ViJTJGTXVsdGlSdW4lMkZzcmMlMkZjb21wb25lbnRzJTJGZWxlbWVudHMlMkZDb29raWVDb25zZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2VsZW1lbnRzL0Nvb2tpZUNvbnNlbnQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fdist%2Freset.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fsrc%2Fcomponents%2Felements%2FCookieConsent.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! __barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js */ \"(ssr)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@ant-design/icons/es/components/AntdIcon.js */ \"(ssr)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMl9fYmFycmVsX29wdGltaXplX18lM0ZuYW1lcyUzREF2YXRhciUyQ0NhcmQlM0ElMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRmFudGQlMkZlcyUyRmluZGV4LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ2FyZCUyMiUyQyUyMk1ldGElMjIlMkMlMjJBdmF0YXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyX19iYXJyZWxfb3B0aW1pemVfXyUzRm5hbWVzJTNEQnV0dG9uJTJDTGF5b3V0JTJDTWVudSUzQSUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmVzJTJGaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJMYXlvdXQlMjIlMkMlMjJNZW51JTIyJTJDJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMl9fYmFycmVsX29wdGltaXplX18lM0ZuYW1lcyUzREJ1dHRvbiUzQSUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmVzJTJGaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJCdXR0b24lMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyX19iYXJyZWxfb3B0aW1pemVfXyUzRm5hbWVzJTNEQ2FyZCUzQSUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmVzJTJGaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDYXJkJTIyJTJDJTIyTWV0YSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJfX2JhcnJlbF9vcHRpbWl6ZV9fJTNGbmFtZXMlM0RDb2wlMkNMYXlvdXQlMkNSb3clM0ElMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRmFudGQlMkZlcyUyRmluZGV4LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGF5b3V0JTIyJTJDJTIyUm93JTIyJTJDJTIyQ29sJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMl9fYmFycmVsX29wdGltaXplX18lM0ZuYW1lcyUzRENvbCUzQSUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGYW50ZCUyRmVzJTJGaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDb2wlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyX19iYXJyZWxfb3B0aW1pemVfXyUzRm5hbWVzJTNEQ29sbGFwc2UlM0ElMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRmFudGQlMkZlcyUyRmluZGV4LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ29sbGFwc2UlMjIlMkMlMjJQYW5lbCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJfX2JhcnJlbF9vcHRpbWl6ZV9fJTNGbmFtZXMlM0RSb3clM0ElMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRmFudGQlMkZlcyUyRmluZGV4LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUm93JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGJTQwYW50LWRlc2lnbiUyRmljb25zJTJGZXMlMkZjb21wb25lbnRzJTJGQW50ZEljb24uanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmRvbmdqaWFuZ3BlbmclMkZEb2N1bWVudHMlMkZXb3JrU3BhY2UlMkZXZWIlMkZNdWx0aVJ1biUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGYXBwLWRpciUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyX19lc01vZHVsZSUyMiUyQyUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBQQUEwTTtBQUMxTTtBQUNBLHdRQUFtTjtBQUNuTjtBQUNBLGdQQUF1TDtBQUN2TDtBQUNBLDRPQUEwTDtBQUMxTDtBQUNBLGdRQUEyTTtBQUMzTTtBQUNBLDBPQUFpTDtBQUNqTDtBQUNBLG9QQUFtTTtBQUNuTTtBQUNBLDBPQUFpTDtBQUNqTDtBQUNBLGdPQUFtSjtBQUNuSjtBQUNBLGdOQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2FyZFwiLFwiTWV0YVwiLFwiQXZhdGFyXCJdICovIFwiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BdmF0YXIsQ2FyZCE9IS9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMYXlvdXRcIixcIk1lbnVcIixcIkJ1dHRvblwiXSAqLyBcIl9fYmFycmVsX29wdGltaXplX18/bmFtZXM9QnV0dG9uLExheW91dCxNZW51IT0hL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkJ1dHRvblwiXSAqLyBcIl9fYmFycmVsX29wdGltaXplX18/bmFtZXM9QnV0dG9uIT0hL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkNhcmRcIixcIk1ldGFcIl0gKi8gXCJfX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUNhcmQhPSEvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTGF5b3V0XCIsXCJSb3dcIixcIkNvbFwiXSAqLyBcIl9fYmFycmVsX29wdGltaXplX18/bmFtZXM9Q29sLExheW91dCxSb3chPSEvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ29sXCJdICovIFwiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2whPSEvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ29sbGFwc2VcIixcIlBhbmVsXCJdICovIFwiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2xsYXBzZSE9IS9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJSb3dcIl0gKi8gXCJfX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPVJvdyE9IS9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvQGFudC1kZXNpZ24vaWNvbnMvZXMvY29tcG9uZW50cy9BbnRkSWNvbi5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvYXBwLWRpci9saW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DAvatar%2CCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%2C%22Avatar%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%2CLayout%2CMenu%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Menu%22%2C%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DButton%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCard%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Card%22%2C%22Meta%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%2CLayout%2CRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Layout%22%2C%22Row%22%2C%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCol%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Col%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DCollapse%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Collapse%22%2C%22Panel%22%5D%7D&modules=%7B%22request%22%3A%22__barrel_optimize__%3Fnames%3DRow%3A%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fantd%2Fes%2Findex.js%22%2C%22ids%22%3A%5B%22Row%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2F%40ant-design%2Ficons%2Fes%2Fcomponents%2FAntdIcon.js%22%2C%22ids%22%3A%5B%22*%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/elements/CookieConsent.tsx":
/*!***************************************************!*\
  !*** ./src/components/elements/CookieConsent.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Layout,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Layout,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Layout,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Layout,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Layout,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst { Footer } = _barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst COOKIE_KEY = 'cookieConsent';\nconst CookieConsent = ()=>{\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const initGoogleAnalytics = ()=>{\n        window.dataLayer = window.dataLayer || [];\n        function gtag(...args) {\n            window.dataLayer.push(args);\n        }\n        if (!document.querySelector('script[src*=\"googletagmanager.com/gtag/js\"]')) {\n            const script = document.createElement('script');\n            script.src = 'https://www.googletagmanager.com/gtag/js?id=G-74Q8HLBVL9';\n            script.async = true;\n            document.head.appendChild(script);\n        }\n        gtag('js', new Date());\n        gtag('config', 'G-74Q8HLBVL9');\n        gtag('consent', 'update', {\n            'ad_storage': 'granted',\n            'analytics_storage': 'granted',\n            'ad_user_data': 'granted',\n            'ad_personalization': 'granted'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CookieConsent.useEffect\": ()=>{\n            const consent = localStorage.getItem(COOKIE_KEY);\n            if (!consent) {\n                setVisible(true);\n            } else if (consent === 'accepted') {\n                initGoogleAnalytics();\n            }\n        }\n    }[\"CookieConsent.useEffect\"], []);\n    const acceptAll = ()=>{\n        localStorage.setItem(COOKIE_KEY, 'accepted');\n        setVisible(false);\n        initGoogleAnalytics();\n    };\n    const rejectAll = ()=>{\n        localStorage.setItem(COOKIE_KEY, 'rejected');\n        setVisible(false);\n        if (typeof window.gtag === 'function') {\n            window.gtag('consent', 'update', {\n                'ad_storage': 'denied',\n                'analytics_storage': 'denied',\n                'ad_user_data': 'denied',\n                'ad_personalization': 'denied'\n            });\n        }\n    };\n    if (!visible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n        style: {\n            position: 'fixed',\n            bottom: 0,\n            width: '100%',\n            padding: 0,\n            zIndex: 1000\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    flexWrap: 'wrap'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Text, {\n                        style: {\n                            flex: '1 1 300px',\n                            minWidth: '300px',\n                            marginRight: '1rem'\n                        },\n                        children: [\n                            \"This website uses cookies for analytics and ad personalization. Please agree to our collection of your data to improve your experience. Read our \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/privacy-policy\",\n                                children: \"Privacy Policy\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 158\n                            }, undefined),\n                            \" and \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/terms-of-use\",\n                                children: \"Terms of Use\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 213\n                            }, undefined),\n                            \".\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        wrap: true,\n                        style: {\n                            flex: '0 0 auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                type: \"primary\",\n                                onClick: acceptAll,\n                                children: \"Agree All\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Layout_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                onClick: rejectAll,\n                                children: \"Only allow necessary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/CookieConsent.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/elements/CookieConsent.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js":
/*!*******************************************************************************!*\
  !*** __barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* reexport safe */ _avatar__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _card__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _avatar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./avatar */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./card */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,Card auto */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BdmF0YXIsQ2FyZCE9IS4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztpRUFFNEM7QUFDSiIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgQXZhdGFyIH0gZnJvbSBcIi4vYXZhdGFyXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FyZCB9IGZyb20gXCIuL2NhcmRcIiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiQXZhdGFyIiwiQ2FyZCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Avatar,Card!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js":
/*!**************************************************************************!*\
  !*** __barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _button__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./button */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* __next_internal_client_entry_do_not_use__ Button auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CdXR0b24hPSEuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OzREQUU0QyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgQnV0dG9uIH0gZnJvbSBcIi4vYnV0dG9uXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJ1dHRvbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Button!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js":
/*!**************************************************************************************!*\
  !*** __barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _button__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Layout: () => (/* reexport safe */ _layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _menu__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./button */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./menu */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* __next_internal_client_entry_do_not_use__ Button,Layout,Menu auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CdXR0b24sTGF5b3V0LE1lbnUhPSEuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozt3RUFFNEM7QUFDQTtBQUNKIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCdXR0b24gfSBmcm9tIFwiLi9idXR0b25cIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMYXlvdXQgfSBmcm9tIFwiLi9sYXlvdXRcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNZW51IH0gZnJvbSBcIi4vbWVudVwiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJCdXR0b24iLCJMYXlvdXQiLCJNZW51Il0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Button,Layout,Menu!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js":
/*!************************************************************************!*\
  !*** __barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* reexport safe */ _card__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _card__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./card */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* __next_internal_client_entry_do_not_use__ Card auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DYXJkIT0hLi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OzswREFFd0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhcmQgfSBmcm9tIFwiLi9jYXJkXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNhcmQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Card!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js":
/*!***********************************************************************!*\
  !*** __barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Col: () => (/* reexport safe */ _col__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _col__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./col */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* __next_internal_client_entry_do_not_use__ Col auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2whPSEuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O3lEQUVzQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ29sIH0gZnJvbSBcIi4vY29sXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNvbCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Col!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js":
/*!**********************************************************************************!*\
  !*** __barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Col: () => (/* reexport safe */ _col__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Layout: () => (/* reexport safe */ _layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Row: () => (/* reexport safe */ _row__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _col__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./col */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _row__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./row */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* __next_internal_client_entry_do_not_use__ Col,Layout,Row auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2wsTGF5b3V0LFJvdyE9IS4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O29FQUVzQztBQUNNO0FBQ04iLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvbCB9IGZyb20gXCIuL2NvbFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExheW91dCB9IGZyb20gXCIuL2xheW91dFwiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFJvdyB9IGZyb20gXCIuL3Jvd1wiIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJDb2wiLCJMYXlvdXQiLCJSb3ciXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Col,Layout,Row!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js":
/*!****************************************************************************!*\
  !*** __barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapse: () => (/* reexport safe */ _collapse__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _collapse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./collapse */ \"(ssr)/./node_modules/antd/es/collapse/index.js\");\n/* __next_internal_client_entry_do_not_use__ Collapse auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Db2xsYXBzZSE9IS4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OERBRWdEIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvYW50ZC9lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2xsYXBzZSB9IGZyb20gXCIuL2NvbGxhcHNlXCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIkNvbGxhcHNlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Collapse!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js":
/*!***********************************************************************!*\
  !*** __barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Row: () => (/* reexport safe */ _row__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _row__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./row */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* __next_internal_client_entry_do_not_use__ Row auto */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Sb3chPSEuL25vZGVfbW9kdWxlcy9hbnRkL2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O3lEQUVzQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vbm9kZV9tb2R1bGVzL2FudGQvZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCB7IGRlZmF1bHQgYXMgUm93IH0gZnJvbSBcIi4vcm93XCIiXSwibmFtZXMiOlsiZGVmYXVsdCIsIlJvdyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Row!=!./node_modules/antd/es/index.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/@rc-component","vendor-chunks/@babel","vendor-chunks/rc-util","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-field-form","vendor-chunks/rc-motion","vendor-chunks/rc-overflow","vendor-chunks/@swc","vendor-chunks/stylis","vendor-chunks/rc-resize-observer","vendor-chunks/rc-input","vendor-chunks/rc-dropdown","vendor-chunks/rc-collapse","vendor-chunks/rc-tooltip","vendor-chunks/rc-textarea","vendor-chunks/react-is","vendor-chunks/rc-picker","vendor-chunks/@emotion","vendor-chunks/classnames","vendor-chunks/toggle-selection","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-pagination","vendor-chunks/copy-to-clipboard"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();