/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #333;
}

/* Layout components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;
}

.col {
  padding: 10px;
}

.col-12 { flex: 0 0 100%; }
.col-6 { flex: 0 0 50%; }
.col-4 { flex: 0 0 33.333%; }
.col-3 { flex: 0 0 25%; }

@media (max-width: 768px) {
  .col-6, .col-4, .col-3 {
    flex: 0 0 100%;
  }
}

/* Header styles */
.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.07);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  z-index: 100;
  position: sticky;
  top: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #2c3e50;
}

.logo img {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
}

.nav {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav a {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s;
}

.nav a:hover {
  color: #1890ff;
}

.btn {
  display: inline-block;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s;
}

.btn:hover {
  background: #40a9ff;
}

.btn-large {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 10px;
  margin: 8px;
}

/* Card styles */
.card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.07);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.card-cover img {
  width: 100%;
  height: auto;
  object-fit: contain;
  background: #f5f5f5;
}

.card-body {
  padding: 20px;
  text-align: center;
}

.card-title {
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
}

.card-description {
  color: #666;
  line-height: 1.5;
  min-height: 100px;
}

/* Footer styles */
.footer {
  background: #2c3e50;
  color: white;
  padding: 50px 0 20px;
  margin-top: 0px;
}

.footer h3 {
  color: white;
  margin-bottom: 20px;
}

.footer ul {
  list-style: none;
  padding: 0;
}

.footer a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s;
}

.footer a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #444;
  color: #aaa;
  margin-top: 40px;
}

/* Section styles */
.section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 50px;
  color: #2c3e50;
}

.hero {
  background: #f7f9fb;
  min-height: 100vh;
  padding: 80px 0 40px 0;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #2c3e50;
}

.hero p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: #666;
}

/* Utility classes */
.text-center { text-align: center; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }
.mb-50 { margin-bottom: 50px; }
.mt-48 { margin-top: 48px; }

.bg-white { background: white; }
.bg-gray { background: #f7f9fb; }

/* Page content styles */
.page-content {
  padding: 2rem 0;
  max-width: 800px;
  margin: 0 auto;
}

.page-content h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.page-content h2 {
  color: #2c3e50;
  margin: 2rem 0 1rem 0;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.5rem;
}

.page-content h3 {
  color: #2c3e50;
  margin: 1.5rem 0 0.5rem 0;
}

.page-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.page-content ul {
  margin: 1rem 0;
  padding-left: 2rem;
}

.page-content li {
  margin-bottom: 0.5rem;
}

.page-content strong {
  color: #2c3e50;
  display: block;
  margin: 1rem 0 0.5rem 0;
}

/* FAQ styles */
.faq-item {
  margin-bottom: 30px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
}

.faq-item h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.faq-item p {
  color: #666;
  margin: 0;
}

/* Hot Apps & Technology Pages Styles */
.main {
  min-height: 100vh;
  background: #f8fafc;
}

/* Utility classes for spacing */
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-12 {
  margin-top: 3rem;
}

.text-center {
  text-align: center;
}

/* Grid layout */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.app-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  text-decoration: none;
  color: inherit;
}

.app-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #3b82f6;
}

.app-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.app-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.app-category {
  display: inline-block;
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.app-category.purple {
  background: #ede9fe;
  color: #7c3aed;
}

.app-category.green {
  background: #dcfce7;
  color: #16a34a;
}

.app-category.yellow {
  background: #fef3c7;
  color: #d97706;
}

.app-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.app-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.app-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.app-link {
  color: #3b82f6;
  font-weight: 500;
  text-decoration: none;
}

.app-link:hover {
  color: #1d4ed8;
}

/* Technology page specific styles */
.tech-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  text-decoration: none;
  color: inherit;
}

.tech-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #7c3aed;
}

.tech-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.tech-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.tech-stars {
  display: flex;
  align-items: center;
  color: #f59e0b;
  font-size: 0.875rem;
  font-weight: 500;
}

.tech-category {
  display: inline-block;
  background: #f3e8ff;
  color: #7c3aed;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.tech-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.tech-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.tech-language {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 0.75rem;
}

.tech-github {
  color: #6b7280;
  font-size: 0.75rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* Page header styles */
.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  max-width: 48rem;
  margin: 0 auto;
  line-height: 1.6;
}

/* Call to action section */
.cta-section {
  text-align: center;
  margin-top: 3rem;
}

.cta-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 2rem;
  color: white;
}

.cta-card.blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.cta-card.purple {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

.cta-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.cta-description {
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.cta-button {
  display: inline-block;
  padding: 0.75rem 2rem;
  background: white;
  color: #1f2937;
  text-decoration: none;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cta-button:hover {
  background: #f3f4f6;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Detail page styles */
.detail-page {
  max-width: 64rem;
  margin: 0 auto;
}

.detail-header {
  text-align: center;
  margin-bottom: 3rem;
}

.detail-icon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
}

.detail-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.detail-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  max-width: 48rem;
  margin: 0 auto 1.5rem;
  line-height: 1.6;
}

.detail-badges {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.detail-badge {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.detail-section {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.detail-section h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.detail-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.detail-list {
  list-style: none;
  padding: 0;
}

.detail-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.detail-list .check {
  color: #10b981;
  margin-right: 0.75rem;
  font-weight: 600;
}

.detail-list .check.blue {
  color: #3b82f6;
}

.use-cases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.use-case {
  background: white;
  border-radius: 12px;
  padding: 1rem;
}

.use-case h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.use-case p {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.back-link {
  display: inline-block;
  color: #7c3aed;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #5b21b6;
}

.back-link.blue {
  color: #3b82f6;
}

.back-link.blue:hover {
  color: #1e40af;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }

  .nav {
    display: none;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .section {
    padding: 40px 0;
  }

  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 1rem;
  }

  .app-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .detail-title {
    font-size: 2rem;
  }

  .detail-icon {
    font-size: 4rem;
  }

  .detail-badges {
    flex-direction: column;
    align-items: center;
  }

  .detail-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .use-cases {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
  }
}
