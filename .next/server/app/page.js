/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"44114a47af24\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjQ0MTE0YTQ3YWYyNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: 'Multi Run - Parallel Dual App',\n    description: 'Multi Run​​ is a powerful tool that allows you to run multiple accounts or applications simultaneously on a single device.',\n    keywords: 'Multi Run,Parallel Space,Dual App,Multiple Accounts',\n    icons: {\n        icon: '/logo.webp'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUtNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtJQUNWQyxPQUFPO1FBQ0xDLE1BQU07SUFDUjtBQUNGLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1osMkpBQWU7c0JBQzdCUTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgU2NyaXB0IGZyb20gJ25leHQvc2NyaXB0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnTXVsdGkgUnVuIC0gUGFyYWxsZWwgRHVhbCBBcHAnLFxuICBkZXNjcmlwdGlvbjogJ011bHRpIFJ1buKAi+KAiyBpcyBhIHBvd2VyZnVsIHRvb2wgdGhhdCBhbGxvd3MgeW91IHRvIHJ1biBtdWx0aXBsZSBhY2NvdW50cyBvciBhcHBsaWNhdGlvbnMgc2ltdWx0YW5lb3VzbHkgb24gYSBzaW5nbGUgZGV2aWNlLicsXG4gIGtleXdvcmRzOiAnTXVsdGkgUnVuLFBhcmFsbGVsIFNwYWNlLER1YWwgQXBwLE11bHRpcGxlIEFjY291bnRzJyxcbiAgaWNvbnM6IHtcbiAgICBpY29uOiAnL2xvZ28ud2VicCcsXG4gIH0sXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsImljb25zIiwiaWNvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_pages_MultipleAccountsPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/pages/MultipleAccountsPage */ \"(rsc)/./src/pages/MultipleAccountsPage.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_pages_MultipleAccountsPage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0U7QUFFckQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHVFQUFvQkE7Ozs7O0FBQzlCIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9hcHAvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE11bHRpcGxlQWNjb3VudHNQYWdlIGZyb20gJy4uL3NyYy9wYWdlcy9NdWx0aXBsZUFjY291bnRzUGFnZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIDxNdWx0aXBsZUFjY291bnRzUGFnZSAvPlxufVxuIl0sIm5hbWVzIjpbIk11bHRpcGxlQWNjb3VudHNQYWdlIiwiSG9tZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/components/elements/DownloadButton.tsx":
/*!****************************************************!*\
  !*** ./src/components/elements/DownloadButton.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst DownloadButton = ({ icon, text, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: \"btn btn-large\",\n        style: {\n            display: 'inline-flex',\n            alignItems: 'center',\n            gap: '8px'\n        },\n        children: [\n            icon,\n            text\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/DownloadButton.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9Eb3dubG9hZEJ1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBUTFCLE1BQU1DLGlCQUFnRCxDQUFDLEVBQUVDLElBQUksRUFBRUMsSUFBSSxFQUFFQyxJQUFJLEVBQUUsaUJBQ3pFLDhEQUFDQztRQUFFRCxNQUFNQTtRQUFNRSxXQUFVO1FBQWdCQyxPQUFPO1lBQUVDLFNBQVM7WUFBZUMsWUFBWTtZQUFVQyxLQUFLO1FBQU07O1lBQ3hHUjtZQUNBQzs7Ozs7OztBQUlMLGlFQUFlRixjQUFjQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9Eb3dubG9hZEJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGludGVyZmFjZSBEb3dubG9hZEJ1dHRvblByb3BzIHtcbiAgaWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbiAgdGV4dDogc3RyaW5nO1xuICBocmVmOiBzdHJpbmc7XG59XG5cbmNvbnN0IERvd25sb2FkQnV0dG9uOiBSZWFjdC5GQzxEb3dubG9hZEJ1dHRvblByb3BzPiA9ICh7IGljb24sIHRleHQsIGhyZWYgfSkgPT4gKFxuICA8YSBocmVmPXtocmVmfSBjbGFzc05hbWU9XCJidG4gYnRuLWxhcmdlXCIgc3R5bGU9e3sgZGlzcGxheTogJ2lubGluZS1mbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzhweCcgfX0+XG4gICAge2ljb259XG4gICAge3RleHR9XG4gIDwvYT5cbik7XG5cbmV4cG9ydCBkZWZhdWx0IERvd25sb2FkQnV0dG9uOyJdLCJuYW1lcyI6WyJSZWFjdCIsIkRvd25sb2FkQnV0dG9uIiwiaWNvbiIsInRleHQiLCJocmVmIiwiYSIsImNsYXNzTmFtZSIsInN0eWxlIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJnYXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/DownloadButton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/FeatureCard.tsx":
/*!*************************************************!*\
  !*** ./src/components/elements/FeatureCard.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst FeatureCard = ({ img, title, desc })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        style: {\n            marginBottom: 24\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-cover\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    alt: title,\n                    src: img\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-body\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"card-title\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"card-description\",\n                        children: desc\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/FeatureCard.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeatureCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9GZWF0dXJlQ2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBUTFCLE1BQU1DLGNBQTBDLENBQUMsRUFBRUMsR0FBRyxFQUFFQyxLQUFLLEVBQUVDLElBQUksRUFBRSxpQkFDbkUsOERBQUNDO1FBQUlDLFdBQVU7UUFBT0MsT0FBTztZQUFFQyxjQUFjO1FBQUc7OzBCQUM5Qyw4REFBQ0g7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNKO29CQUFJTyxLQUFLTjtvQkFBT08sS0FBS1I7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0c7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSzt3QkFBR0wsV0FBVTtrQ0FBY0g7Ozs7OztrQ0FDNUIsOERBQUNTO3dCQUFFTixXQUFVO2tDQUFvQkY7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUt2QyxpRUFBZUgsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vc3JjL2NvbXBvbmVudHMvZWxlbWVudHMvRmVhdHVyZUNhcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgRmVhdHVyZUNhcmRQcm9wcyB7XG4gIGltZzogc3RyaW5nO1xuICB0aXRsZTogc3RyaW5nO1xuICBkZXNjOiBzdHJpbmc7XG59XG5cbmNvbnN0IEZlYXR1cmVDYXJkOiBSZWFjdC5GQzxGZWF0dXJlQ2FyZFByb3BzPiA9ICh7IGltZywgdGl0bGUsIGRlc2MgfSkgPT4gKFxuICA8ZGl2IGNsYXNzTmFtZT1cImNhcmRcIiBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDI0IH19PlxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1jb3ZlclwiPlxuICAgICAgPGltZyBhbHQ9e3RpdGxlfSBzcmM9e2ltZ30gLz5cbiAgICA8L2Rpdj5cbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtYm9keVwiPlxuICAgICAgPGgzIGNsYXNzTmFtZT1cImNhcmQtdGl0bGVcIj57dGl0bGV9PC9oMz5cbiAgICAgIDxwIGNsYXNzTmFtZT1cImNhcmQtZGVzY3JpcHRpb25cIj57ZGVzY308L3A+XG4gICAgPC9kaXY+XG4gIDwvZGl2PlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgRmVhdHVyZUNhcmQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJGZWF0dXJlQ2FyZCIsImltZyIsInRpdGxlIiwiZGVzYyIsImRpdiIsImNsYXNzTmFtZSIsInN0eWxlIiwibWFyZ2luQm90dG9tIiwiYWx0Iiwic3JjIiwiaDMiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/FeatureCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/Footer.tsx":
/*!********************************************!*\
  !*** ./src/components/elements/Footer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Footer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"footer\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"row\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col col-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"Multi Run\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 9,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Run multiple accounts and apps simultaneously on one device with complete data isolation.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 10,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col col-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#features\",\n                                                children: \"Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 15,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 15,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#download\",\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 16,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 16,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col col-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/privacy-policy\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/terms-of-use\",\n                                                children: \"Terms of Use\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"footer-bottom\",\n                    children: \"\\xa9 2024 Multi Run. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n            lineNumber: 6,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/Footer.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/NavBar.tsx":
/*!********************************************!*\
  !*** ./src/components/elements/NavBar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst NavBar = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"header\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"header-content\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"logo\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/logo.webp\",\n                            alt: \"Multiple Accounts Logo\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"logo-text\",\n                            children: \"Multi Run\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"nav\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/#features\",\n                                    children: \"Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                    lineNumber: 13,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/#download\",\n                                    children: \"Download\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/#faq\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/#testimonials\",\n                                    children: \"Testimonials\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/#download\",\n                    className: \"btn\",\n                    children: \"Download Now\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n            lineNumber: 6,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/NavBar.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NavBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/NavBar.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/SectionTitle.tsx":
/*!**************************************************!*\
  !*** ./src/components/elements/SectionTitle.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SectionTitle = ({ children, style })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n        className: \"section-title\",\n        style: style,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/SectionTitle.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SectionTitle);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9lbGVtZW50cy9TZWN0aW9uVGl0bGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQjtBQUUxQixNQUFNQyxlQUFtRixDQUFDLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFLGlCQUMzRyw4REFBQ0M7UUFBR0MsV0FBVTtRQUFnQkYsT0FBT0E7a0JBQVFEOzs7Ozs7QUFHL0MsaUVBQWVELFlBQVlBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2VsZW1lbnRzL1NlY3Rpb25UaXRsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuY29uc3QgU2VjdGlvblRpdGxlOiBSZWFjdC5GQzxSZWFjdC5Qcm9wc1dpdGhDaGlsZHJlbjx7IHN0eWxlPzogUmVhY3QuQ1NTUHJvcGVydGllcyB9Pj4gPSAoeyBjaGlsZHJlbiwgc3R5bGUgfSkgPT4gKFxuICA8aDIgY2xhc3NOYW1lPVwic2VjdGlvbi10aXRsZVwiIHN0eWxlPXtzdHlsZX0+e2NoaWxkcmVufTwvaDI+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBTZWN0aW9uVGl0bGU7Il0sIm5hbWVzIjpbIlJlYWN0IiwiU2VjdGlvblRpdGxlIiwiY2hpbGRyZW4iLCJzdHlsZSIsImgyIiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/SectionTitle.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/elements/TestimonialCard.tsx":
/*!*****************************************************!*\
  !*** ./src/components/elements/TestimonialCard.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst TestimonialCard = ({ avatar, name, text })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        style: {\n            marginBottom: 24\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card-body\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        marginBottom: 16\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: avatar,\n                            alt: name,\n                            style: {\n                                width: 48,\n                                height: 48,\n                                borderRadius: '50%',\n                                marginRight: 16,\n                                objectFit: 'cover'\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    fontWeight: 'bold',\n                                    color: '#2c3e50',\n                                    margin: 0\n                                },\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: '#666',\n                        fontSize: '1rem',\n                        margin: 0\n                    },\n                    children: text\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/elements/TestimonialCard.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestimonialCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/elements/TestimonialCard.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Column.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Column.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Column = ({ xs, md, lg, children, style })=>{\n    let className = 'col';\n    // Simple responsive logic\n    if (lg === 6) className += ' col-3';\n    else if (md === 12) className += ' col-6';\n    else if (xs === 24) className += ' col-12';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: style,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Column.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Column);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvQ29sdW1uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFVMUIsTUFBTUMsU0FBZ0MsQ0FBQyxFQUFFQyxFQUFFLEVBQUVDLEVBQUUsRUFBRUMsRUFBRSxFQUFFQyxRQUFRLEVBQUVDLEtBQUssRUFBRTtJQUNwRSxJQUFJQyxZQUFZO0lBRWhCLDBCQUEwQjtJQUMxQixJQUFJSCxPQUFPLEdBQUdHLGFBQWE7U0FDdEIsSUFBSUosT0FBTyxJQUFJSSxhQUFhO1NBQzVCLElBQUlMLE9BQU8sSUFBSUssYUFBYTtJQUVqQyxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBV0E7UUFBV0QsT0FBT0E7a0JBQy9CRDs7Ozs7O0FBR1A7QUFFQSxpRUFBZUosTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L0NvbHVtbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIENvbHVtblByb3BzIHtcbiAgeHM/OiBudW1iZXI7XG4gIG1kPzogbnVtYmVyO1xuICBsZz86IG51bWJlcjtcbiAgc3R5bGU/OiBSZWFjdC5DU1NQcm9wZXJ0aWVzO1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5jb25zdCBDb2x1bW46IFJlYWN0LkZDPENvbHVtblByb3BzPiA9ICh7IHhzLCBtZCwgbGcsIGNoaWxkcmVuLCBzdHlsZSB9KSA9PiB7XG4gIGxldCBjbGFzc05hbWUgPSAnY29sJztcblxuICAvLyBTaW1wbGUgcmVzcG9uc2l2ZSBsb2dpY1xuICBpZiAobGcgPT09IDYpIGNsYXNzTmFtZSArPSAnIGNvbC0zJztcbiAgZWxzZSBpZiAobWQgPT09IDEyKSBjbGFzc05hbWUgKz0gJyBjb2wtNic7XG4gIGVsc2UgaWYgKHhzID09PSAyNCkgY2xhc3NOYW1lICs9ICcgY29sLTEyJztcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHN0eWxlPXtzdHlsZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb2x1bW47Il0sIm5hbWVzIjpbIlJlYWN0IiwiQ29sdW1uIiwieHMiLCJtZCIsImxnIiwiY2hpbGRyZW4iLCJzdHlsZSIsImNsYXNzTmFtZSIsImRpdiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Column.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Container.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/Container.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Container = ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            maxWidth: 1200,\n            margin: '0 auto',\n            padding: '0 20px'\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Container.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Container);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvQ29udGFpbmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFMUIsTUFBTUMsWUFBK0MsQ0FBQyxFQUFFQyxRQUFRLEVBQUUsaUJBQ2hFLDhEQUFDQztRQUFJQyxPQUFPO1lBQUVDLFVBQVU7WUFBTUMsUUFBUTtZQUFVQyxTQUFTO1FBQVM7a0JBQUlMOzs7Ozs7QUFHeEUsaUVBQWVELFNBQVNBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kb25namlhbmdwZW5nL0RvY3VtZW50cy9Xb3JrU3BhY2UvV2ViL011bHRpUnVuL3NyYy9jb21wb25lbnRzL2xheW91dC9Db250YWluZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IENvbnRhaW5lcjogUmVhY3QuRkM8UmVhY3QuUHJvcHNXaXRoQ2hpbGRyZW4+ID0gKHsgY2hpbGRyZW4gfSkgPT4gKFxuICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAxMjAwLCBtYXJnaW46ICcwIGF1dG8nLCBwYWRkaW5nOiAnMCAyMHB4JyB9fT57Y2hpbGRyZW59PC9kaXY+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBDb250YWluZXI7ICJdLCJuYW1lcyI6WyJSZWFjdCIsIkNvbnRhaW5lciIsImNoaWxkcmVuIiwiZGl2Iiwic3R5bGUiLCJtYXhXaWR0aCIsIm1hcmdpbiIsInBhZGRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Container.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Row.tsx":
/*!***************************************!*\
  !*** ./src/components/layout/Row.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Row = ({ children, style, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"row\",\n        style: style,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/components/layout/Row.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Row);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvUm93LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFTMUIsTUFBTUMsTUFBMEIsQ0FBQyxFQUFFQyxRQUFRLEVBQUVDLEtBQUssRUFBRSxHQUFHQyxPQUFPLGlCQUM1RCw4REFBQ0M7UUFBSUMsV0FBVTtRQUFNSCxPQUFPQTtrQkFDekJEOzs7Ozs7QUFJTCxpRUFBZUQsR0FBR0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL2RvbmdqaWFuZ3BlbmcvRG9jdW1lbnRzL1dvcmtTcGFjZS9XZWIvTXVsdGlSdW4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L1Jvdy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIFJvd1Byb3BzIHtcbiAgZ3V0dGVyPzogW251bWJlciwgbnVtYmVyXTtcbiAgYWxpZ24/OiBzdHJpbmc7XG4gIHN0eWxlPzogUmVhY3QuQ1NTUHJvcGVydGllcztcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgUm93OiBSZWFjdC5GQzxSb3dQcm9wcz4gPSAoeyBjaGlsZHJlbiwgc3R5bGUsIC4uLnByb3BzIH0pID0+IChcbiAgPGRpdiBjbGFzc05hbWU9XCJyb3dcIiBzdHlsZT17c3R5bGV9PlxuICAgIHtjaGlsZHJlbn1cbiAgPC9kaXY+XG4pO1xuXG5leHBvcnQgZGVmYXVsdCBSb3c7Il0sIm5hbWVzIjpbIlJlYWN0IiwiUm93IiwiY2hpbGRyZW4iLCJzdHlsZSIsInByb3BzIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/layout/Row.tsx\n");

/***/ }),

/***/ "(rsc)/./src/pages/MultipleAccountsPage.tsx":
/*!********************************************!*\
  !*** ./src/pages/MultipleAccountsPage.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_elements_NavBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/elements/NavBar */ \"(rsc)/./src/components/elements/NavBar.tsx\");\n/* harmony import */ var _components_elements_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/elements/Footer */ \"(rsc)/./src/components/elements/Footer.tsx\");\n/* harmony import */ var _components_elements_FeatureCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/elements/FeatureCard */ \"(rsc)/./src/components/elements/FeatureCard.tsx\");\n/* harmony import */ var _components_elements_TestimonialCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/elements/TestimonialCard */ \"(rsc)/./src/components/elements/TestimonialCard.tsx\");\n/* harmony import */ var _components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/elements/SectionTitle */ \"(rsc)/./src/components/elements/SectionTitle.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/layout/Container */ \"(rsc)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_layout_Row__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/layout/Row */ \"(rsc)/./src/components/layout/Row.tsx\");\n/* harmony import */ var _components_layout_Column__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/layout/Column */ \"(rsc)/./src/components/layout/Column.tsx\");\n/* harmony import */ var _components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/elements/DownloadButton */ \"(rsc)/./src/components/elements/DownloadButton.tsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst features = [\n    {\n        img: '/multi_feature_1.webp',\n        title: 'App Cloning',\n        desc: 'Clone and run multiple instances of the same app with separate data storage.'\n    },\n    {\n        img: '/multi_feature_2.webp',\n        title: 'Data Isolation',\n        desc: 'Keep your accounts secure with isolated data spaces and no cross-contamination.'\n    },\n    {\n        img: '/multi_feature_3.webp',\n        title: 'Parallel Running',\n        desc: 'Run multiple accounts simultaneously without performance issues.'\n    },\n    {\n        img: '/multi_feature_4.webp',\n        title: 'Easy Switching',\n        desc: 'Quickly switch between accounts with our intuitive interface.'\n    }\n];\nconst faqs = [\n    {\n        question: 'How do I clone an app?',\n        answer: 'Simply open Multi Run, select the app you want to clone, and follow the on-screen instructions.'\n    },\n    {\n        question: 'Is Multi Run free?',\n        answer: 'Yes, Multi Run is free to use for all users.'\n    },\n    {\n        question: 'Will my data be safe and isolated?',\n        answer: 'Yes, each cloned app runs in a separate, isolated environment to keep your data secure.'\n    },\n    {\n        question: 'Which platforms are supported?',\n        answer: 'Multi Run is available for Android.'\n    }\n];\nconst testimonials = [\n    {\n        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',\n        name: 'Alex',\n        text: 'Ever since I started using it, I enjoyed every bit of it. It\\'s very simple to use and has a great interface. The experience so far is great. Worth 5 stars!'\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',\n        name: 'Linda',\n        text: \"i can't really believe my eyes it's really totaly free and there is no ads u can add multiple apps with free and quickly thank you so much for the creator 😙😙😍really advice everyone to try it it's better than others apps\"\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/men/65.jpg',\n        name: 'Sam',\n        text: 'Finally an app which works with WhatsApp and with less ads. As an Android developer, I understand the need of a foreground notification. But the app shows two foreground notifications.'\n    },\n    {\n        avatar: 'https://randomuser.me/api/portraits/women/68.jpg',\n        name: 'Emily',\n        text: 'I tried so many clone apps for this particular app and all didn\\'t work except this one! Simple and plays its role very nicely.'\n    }\n];\nconst MultipleAccountsPage = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_NavBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 79,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: '#f7f9fb',\n                    minHeight: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            gutter: [\n                                40,\n                                40\n                            ],\n                            align: \"middle\",\n                            style: {\n                                padding: '80px 0 40px 0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    xs: 24,\n                                    md: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: 20,\n                                                color: '#2c3e50'\n                                            },\n                                            children: \"Run Multi Accounts on One Device\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: '1.1rem',\n                                                marginBottom: 30,\n                                                color: '#666'\n                                            },\n                                            children: \"Keep your personal and professional lives separate with our secure app cloning solution. No more switching between accounts - run them all simultaneously!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            text: \"Get Started\",\n                                            href: \"https://play.google.com/store/apps/details?id=com.dong.multirun\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    xs: 24,\n                                    md: 12,\n                                    style: {\n                                        textAlign: 'center'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/multi_title_image.webp\",\n                                        alt: \"Multi Run\",\n                                        style: {\n                                            maxWidth: '100%',\n                                            height: 'auto',\n                                            borderRadius: 10,\n                                            boxShadow: '0 4px 16px rgba(52,152,219,0.08)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f9f9f9',\n                            padding: '80px 0'\n                        },\n                        id: \"features\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    gutter: [\n                                        30,\n                                        30\n                                    ],\n                                    children: features.map((f)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_FeatureCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                ...f\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, f.title, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 0',\n                            textAlign: 'center'\n                        },\n                        id: \"download\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: \"Download Multi Run\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Available on all major platforms. Get started today!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_DownloadButton__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    text: \"Google Play\",\n                                    href: \"https://play.google.com/store/apps/details?id=com.dong.multirun\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#fff',\n                            padding: '80px 0'\n                        },\n                        id: \"faq\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        maxWidth: 800,\n                                        margin: '0 auto'\n                                    },\n                                    children: faqs.map((f)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                marginBottom: 30,\n                                                padding: 20,\n                                                background: '#f9f9f9',\n                                                borderRadius: 10\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#2c3e50',\n                                                        marginBottom: 10\n                                                    },\n                                                    children: f.question\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#666',\n                                                        margin: 0\n                                                    },\n                                                    children: f.answer\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, f.question, true, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f9f9f9',\n                            padding: '80px 0'\n                        },\n                        id: \"testimonials\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_SectionTitle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: \"What Users Say\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Row__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    gutter: [\n                                        30,\n                                        30\n                                    ],\n                                    children: testimonials.map((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Column__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_TestimonialCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                ...t\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, t.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 80,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_elements_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/WorkSpace/Web/MultiRun/src/pages/MultipleAccountsPage.tsx\",\n                lineNumber: 145,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultipleAccountsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/pages/MultipleAccountsPage.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZG9uZ2ppYW5ncGVuZyUyRkRvY3VtZW50cyUyRldvcmtTcGFjZSUyRldlYiUyRk11bHRpUnVuJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZhcHAtZGlyJTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ05BQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZG9uZ2ppYW5ncGVuZy9Eb2N1bWVudHMvV29ya1NwYWNlL1dlYi9NdWx0aVJ1bi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9hcHAtZGlyL2xpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdongjiangpeng%2FDocuments%2FWorkSpace%2FWeb%2FMultiRun&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();